import { describe, it, expect, beforeEach } from 'vitest'
import { Downloader, DownloadOptions } from './downloader'
import path from 'path'

describe('Downloader', () => {
	let downloader: Downloader

	beforeEach(() => {
		const save_path = path.join(__dirname, '..', '..', 'downloader')
		const opts: DownloadOptions = {
			cache_package_name: 'main-ui.79cd384340d1452d872d6e909183119e.zip',
			cache_path: save_path,
			package_name: 'main-ui',
			url: 'https://probe-1318590712.cos.ap-beijing.myqcloud.com/pc-nova/bundles/nova_ui.zip?m=79cd384340d1452d872d6e909183119e'
		}
		downloader = new Downloader(opts)
	})

	it('should create a downloader instance', () => {
		expect(downloader).toBeInstanceOf(Downloader)
	})

	// 尝试下载一个文件
	it('should download test file success', () => {
		downloader.start()
	})

	// 添加更多测试用例...
})
