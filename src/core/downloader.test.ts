import { describe, it, expect, beforeEach } from 'vitest'
import { Downloader, DownloadOptions } from './downloader'
import path from 'path'
// import { sleep } from '../../tests/helpers/test-utils'

describe('Downloader', () => {
	let downloader: Downloader

	beforeEach(() => {
		const save_path = path.join(__dirname, '..', '..', 'downloader')
		console.log('测试下载目录：', save_path)
		const opts: DownloadOptions = {
			cache_path: save_path,
			package_name: 'main-ui.79cd384340d1452d872d6e909183119e.zip',
			url: 'https://probe-1318590712.cos.ap-beijing.myqcloud.com/pc-nova/bundles/nova_ui.zip?m=79cd384340d1452d872d6e909183119e',
      md5: 'test'
		}
		downloader = new Downloader(opts)
	})

	it('should create a downloader instance', () => {
		expect(downloader).toBeInstanceOf(Downloader)
	})

	// 尝试下载一个文件
	it('should download test file success', async () => {
		await downloader.start()
	})

  // 测试下载进度
  it('should emit progress events', async () => {
  })


	// 添加更多测试用例...
})
