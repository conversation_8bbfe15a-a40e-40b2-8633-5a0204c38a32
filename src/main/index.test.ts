import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createMockBrowserWindow } from '@tests/helpers/test-utils'

// Mock electron modules
vi.mock('electron', () => ({
  app: {
    whenReady: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    quit: vi.fn()
  },
  BrowserWindow: vi.fn().mockImplementation(() => createMockBrowserWindow()),
  ipcMain: {
    on: vi.fn()
  },
  shell: {
    openExternal: vi.fn()
  }
}))

// Mock @electron-toolkit/utils
vi.mock('@electron-toolkit/utils', () => ({
  electronApp: {
    setAppUserModelId: vi.fn()
  },
  optimizer: {
    watchWindowShortcuts: vi.fn()
  },
  is: {
    dev: false
  }
}))

// Mock path module
vi.mock('path', () => ({
  join: vi.fn((...args) => args.join('/'))
}))

// Mock icon import
vi.mock('../../resources/icon.png?inline', () => 'mocked-icon-path')

// Mock core modules
vi.mock('../core/electron-main', () => ({
  electronMain: {
    onRendererEvent: vi.fn(),
    broadcast: vi.fn(),
    openMainWindow: vi.fn()
  }
}))

vi.mock('../core/UpdaterWindow', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      on: vi.fn(),
      start: vi.fn(),
      close: vi.fn()
    }))
  }
})

describe('Main Process', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createWindow function', () => {
    it('should create a BrowserWindow with correct configuration', async () => {
      // 动态导入主进程模块以确保模拟生效
      await import('../main/index')
      
      const { BrowserWindow } = await import('electron')
      
      // 验证 BrowserWindow 被调用
      expect(BrowserWindow).toHaveBeenCalled()
      
      // 验证配置参数
      const callArgs = (BrowserWindow as any).mock.calls[0][0]
      expect(callArgs).toMatchObject({
        width: 900,
        height: 670,
        show: false,
        autoHideMenuBar: true
      })
      
      expect(callArgs.webPreferences).toMatchObject({
        preload: expect.stringContaining('preload/index.js'),
        sandbox: false
      })
    })
  })

  describe('app lifecycle', () => {
    it('should set up app ready handler', async () => {
      const { app } = await import('electron')
      
      await import('../main/index')
      
      expect(app.whenReady).toHaveBeenCalled()
    })

    it('should set up window-all-closed handler', async () => {
      const { app } = await import('electron')
      
      await import('../main/index')
      
      expect(app.on).toHaveBeenCalledWith('window-all-closed', expect.any(Function))
    })

    it('should set up activate handler', async () => {
      const { app } = await import('electron')
      
      await import('../main/index')
      
      expect(app.on).toHaveBeenCalledWith('activate', expect.any(Function))
    })
  })

  describe('IPC setup', () => {
    it('should set up ping IPC handler', async () => {
      const { ipcMain } = await import('electron')
      
      await import('../main/index')
      
      expect(ipcMain.on).toHaveBeenCalledWith('ping', expect.any(Function))
    })
  })

  describe('electron-main integration', () => {
    it('should set up renderer event handlers', async () => {
      const { electronMain } = await import('../core/electron-main')
      
      await import('../main/index')
      
      expect(electronMain.onRendererEvent).toHaveBeenCalledWith('user-action', expect.any(Function))
      expect(electronMain.onRendererEvent).toHaveBeenCalledWith('error-occurred', expect.any(Function))
    })

    it('should broadcast app-ready event', async () => {
      const { electronMain } = await import('../core/electron-main')
      
      await import('../main/index')
      
      expect(electronMain.broadcast).toHaveBeenCalledWith('app-ready', undefined)
    })
  })

  describe('UpdaterWindow integration', () => {
    it('should create and start UpdaterWindow', async () => {
      const UpdaterWindow = (await import('../core/UpdaterWindow')).default
      
      await import('../main/index')
      
      expect(UpdaterWindow).toHaveBeenCalled()
      
      const updaterInstance = (UpdaterWindow as any).mock.results[0].value
      expect(updaterInstance.on).toHaveBeenCalledWith('open-main-window', expect.any(Function))
      expect(updaterInstance.start).toHaveBeenCalled()
    })
  })
})
